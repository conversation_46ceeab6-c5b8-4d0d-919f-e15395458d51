"use client"

import { User } from '@supabase/supabase-js';
import { createClient } from '@/utils/supabase/client';
import { useState, useEffect } from 'react';

export function useUser() {
  const [user, setUser] = useState<User | null>(null);
  const [role, setRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();

    const fetchUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) {
        console.error("Error fetching user:", error);
        setUser(null);
        setRole(null);
      } else {
        setUser(user);
        if (user?.user_metadata?.role) {
          setRole(user.user_metadata.role as string);
        } else {
          setRole("customer"); // Default role if not set
        }
      }
      setLoading(false);
    };

    fetchUser();

    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        setUser(session?.user || null);
        if (session?.user?.user_metadata?.role) {
          setRole(session.user.user_metadata.role as string);
        } else {
          setRole("customer");
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setRole(null);
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  return { user, role, loading };
}
