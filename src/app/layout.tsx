import "@/styles/globals.css";

import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata = {
  title: "Zeeguros",
  description: "Plataforma de seguros",
  icons: {
    icon: [
      { url: "/icon.svg", type: "image/svg+xml" },
      { url: "/zeeguros-icon-rounded.svg", type: "image/svg+xml" }
    ],
    apple: "/zeeguros-icon-rounded.svg",
    shortcut: "/zeeguros-icon-rounded.svg",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // This suppresses the hydration warning caused by browser extensions
  // that add attributes to the body element
  const suppressHydrationWarning = true;

  return (
    <html lang="es">
      <body className={`font-sans ${inter.variable}`} suppressHydrationWarning={suppressHydrationWarning}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
