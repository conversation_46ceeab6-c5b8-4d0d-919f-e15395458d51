'use client'

import { AppSidebar } from "@/components/app-sidebar";
import { DashboardHeader } from "./dashboard-header";
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar";

interface DashboardLayoutClientProps {
  userInitials: string;
  children: React.ReactNode;
}

export function DashboardLayoutClient({ userInitials, children }: DashboardLayoutClientProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex-1">
        <SidebarProvider
          style={{
            "--sidebar-width": "19rem",
          } as React.CSSProperties}
        >
          <AppSidebar userRole="customer" />
          <SidebarInset>
            <DashboardHeader userInitials={userInitials} />
            <main className="flex w-full flex-col overflow-hidden p-4">
              {children}
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    </div>
  );
}
