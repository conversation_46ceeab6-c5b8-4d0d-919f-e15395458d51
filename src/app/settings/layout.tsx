import { DashboardLayoutClient } from "@/app/(dashboard)/_components/dashboard-layout-client";
import { createClient } from "@/utils/supabase/server";
import { extractUserName } from "@/lib/user-metadata";

export default async function SettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const { firstName } = user ? extractUserName(user) : { firstName: "" };

  // Get user initials for avatar
  const userInitials = firstName && firstName.charAt(0) || "Z";

  return <DashboardLayoutClient userInitials={userInitials}>{children}</DashboardLayoutClient>;
}
