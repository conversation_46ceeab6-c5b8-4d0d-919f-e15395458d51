"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

import { createClient } from "@/utils/supabase/client";
import { NewPolicySteps } from "./_components/NewPolicySteps";
import { Button } from "@/components/ui/button";

export default function NewPolicyPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  // Get the user on component mount
  useEffect(() => {
    async function getUser() {
      try {
        const { data } = await supabase.auth.getUser();
        setUser(data.user);
        setIsLoading(false);
      } catch (error) {
        console.error("Error getting user:", error);
        setIsLoading(false);
      }
    }

    getUser();
  }, [supabase.auth]);

  // Check authentication
  if (!isLoading) {
    if (!user) {
      // If not logged in, redirect to login
      router.push("/login");
    } else if (loading) {
      // If authenticated, set loading to false
      setLoading(false);
    }
  }

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/policies">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a mis pólizas
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">Nueva Póliza</h1>
        <p className="text-muted-foreground mt-2">Registra una nueva póliza en la plataforma Zeeguros</p>
      </div>

      {/* Demo mode banner removed */}

      <NewPolicySteps />
    </div>
  );
}
