"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { format, addHours } from "date-fns";
import { es } from "date-fns/locale";

interface AuctionDurationSelectProps {
  selectedDuration: 24 | 48 | 72 | null;
  onDurationSelect: (duration: 24 | 48 | 72) => void;
  onBack: () => void;
  onContinue: () => void;
}

export function AuctionDurationSelect({
  selectedDuration,
  onDurationSelect,
  onBack,
  onContinue,
}: AuctionDurationSelectProps) {
  const now = new Date();

  const durations = [
    {
      hours: 24,
      label: "24 horas",
      description: "Resultados más rápidos",
      startTime: now,
      endTime: addHours(now, 24),
    },
    {
      hours: 48,
      label: "48 horas",
      description: "Tiempo recomendado",
      startTime: now,
      endTime: addHours(now, 48),
    },
    {
      hours: 72,
      label: "72 horas",
      description: "Más ofertas potenciales",
      startTime: now,
      endTime: addHours(now, 72),
    },
  ];

  const formatDate = (date: Date) => {
    return format(date, "EEEE d 'de' MMMM 'a las' HH:mm", { locale: es });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2
          className="text-2xl font-semibold tracking-tight"
        >
          Selecciona el período de subasta
        </h2>
        <p className="text-muted-foreground">
          Elige cuánto tiempo deseas que dure la subasta de tu póliza. Un
          período más largo puede atraer más ofertas y mejores precios.
        </p>
      </div>

      <div className="space-y-4">
        {durations.map((duration) => (
          <Card
            key={duration.hours}
            className={`cursor-pointer border-2 transition-all ${
              selectedDuration === duration.hours
                ? "border-zeeguros-green"
                : "hover:border-gray-300"
            }`}
            onClick={() => onDurationSelect(duration.hours as 24 | 48 | 72)}
          >
            <CardContent className="p-4">
              <div
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`rounded-full p-2 ${
                      selectedDuration === duration.hours
                        ? "bg-zeeguros-green/10 text-zeeguros-green"
                        : "bg-gray-100 text-gray-500"
                    }`}
                  >
                    <Clock className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">
                      {duration.label}
                    </h3>
                    <p
                      className="text-sm text-muted-foreground"
                    >
                      {duration.description}
                    </p>
                  </div>
                </div>
                <div
                  className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedDuration === duration.hours
                      ? "border-zeeguros-green bg-zeeguros-green"
                      : "border-gray-300"
                  }`}
                >
                  {selectedDuration === duration.hours && (
                    <div
                      className="w-2 h-2 rounded-full bg-white"
                    />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedDuration && (
        <Card className="bg-muted/50">
          <CardContent className="p-4">
            <h3 className="font-medium mb-2">
              Período seleccionado
            </h3>
            <p className="text-sm">
              Desde:{" "}
              <span className="font-medium">
                {formatDate(now)}
              </span>
            </p>
            <p className="text-sm">
              Hasta:{" "}
              <span className="font-medium">
                {formatDate(addHours(now, selectedDuration))}
              </span>
            </p>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between">
        <Button
          type="button"
          className="btn-zeeguros-back"
          onClick={onBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Atrás
        </Button>
        <Button
          type="button"
          className="btn-zeeguros"
          onClick={onContinue}
          disabled={!selectedDuration}
        >
          Continuar
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
