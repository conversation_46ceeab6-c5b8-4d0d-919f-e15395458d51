"use client";

import { Check<PERSON>ircle, FileText, Home, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PolicySuccessProps {
  policyNumber?: string;
}

export function PolicySuccess({ policyNumber }: PolicySuccessProps) {
  return (
    <div className="space-y-8 text-center">
      <div
        className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-green-100"
      >
        <CheckCircle className="h-12 w-12 text-green-600" />
      </div>

      <div className="space-y-2">
        <h1 className="text-2xl font-bold">
          ¡PÓLIZA REGISTRADA!
        </h1>
        <p className="text-muted-foreground">
          Tu póliza{" "}
          {policyNumber && (
            <span className="font-medium">
              {policyNumber}
            </span>
          )}{" "}
          ha sido registrada y la subasta ha comenzado.
        </p>
      </div>

      <div className="mx-auto max-w-lg space-y-6">
        <div className="text-center bg-muted p-4 rounded-md">
          <p className="text-sm text-muted-foreground">
            Las aseguradoras ahora competirán por ofrecerte el mejor precio. Te
            notificaremos por correo electrónico cuando recibas nuevas ofertas.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <Button
            asChild
            className="w-full btn-zeeguros"
          >
            <a href="/policies">
              <Clock className="mr-2 h-4 w-4" />
              Ver mi subasta
            </a>
          </Button>

          <Button
            variant="outline"
            asChild
            className="w-full"
          >
            <a href="/dashboard">
              <Home className="mr-2 h-4 w-4" />
              Ir al Dashboard
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}
