"use client";

import { Check } from "lucide-react";

interface Step {
  title: string;
  description: string;
}

interface PolicyStepperProps {
  currentStep: number;
  steps: Step[];
}

export function PolicyStepper({
  currentStep,
  steps,
}: PolicyStepperProps) {
  return (
    <div className="w-full">
      <div className="relative">
        {/* Progress line background */}
        <div className="absolute top-5 left-0 right-0 h-0.5 bg-muted-foreground/30"></div>

        {/* Progress line active */}
        <div
          className="absolute top-5 left-0 h-0.5 bg-zeeguros-accent transition-all duration-500"
          style={{
            width: `${(currentStep / (steps.length - 1)) * 100}%`
          }}
        ></div>

        <ol className="relative grid grid-cols-5 text-sm font-medium text-center text-muted-foreground">
          {steps.map((step, index) => {
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            return (
              <li key={index} className="flex flex-col items-center gap-1">
                <div
                  className={`z-10 flex items-center justify-center w-10 h-10 rounded-full transition-colors ${
                    isActive
                      ? "bg-zeeguros-accent text-white"
                      : isCompleted
                        ? "bg-zeeguros-accent text-white"
                        : "bg-muted-foreground/10 text-muted-foreground"
                  }`}
                >
                  {isCompleted || (isActive && index === steps.length - 1) ? (
                    <Check className="w-6 h-6" />
                  ) : (
                    index + 1
                  )}
                </div>
                <span className="text-xs sm:text-sm">
                  {step.title}
                </span>
              </li>
            );
          })}
        </ol>
      </div>

      <div className="mt-2 text-sm text-center text-muted-foreground">
        {steps[currentStep]?.description}
      </div>
    </div>
  );
}
