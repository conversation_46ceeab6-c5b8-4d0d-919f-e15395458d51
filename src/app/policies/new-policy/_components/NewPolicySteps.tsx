import { FileUpload } from "@/components/ui/file-upload";
import { PolicyDataForm } from "./PolicyDataForm";
import { PolicyReview } from "./PolicyReview";
import { PolicySuccess } from "./PolicySuccess";
import { AuctionDurationSelect } from "./AuctionDurationSelect";
import { PolicyStepper } from "./PolicyStepper";
import { useNewPolicy } from "../_hooks/useNewPolicy";

export function NewPolicySteps() {
  const {
    currentStep,
    policyData,
    isSubmitting,
    auctionDuration,
    handleFileUploadComplete,
    handleDataUpdate,
    handleAuctionDurationSelect,
    handleContinue,
    handleBack,
    handleSubmit,
  } = useNewPolicy();

  const steps = [
    { title: "Subir documento", description: "Sube tu póliza de seguro" },
    {
      title: "Revisión de datos",
      description: "Revisa y edita los datos extraídos",
    },
    {
      title: "Período de subasta",
      description: "Selecciona la duración de la subasta",
    },
    {
      title: "Confirmar",
      description: "Verifica que toda la información sea correcta",
    },
    {
      title: "Éxito",
      description: "La póliza ha sido registrada correctamente",
    },
  ];

  return (
    <div className="space-y-8">
      <PolicyStepper
        currentStep={currentStep}
        steps={steps}
      />

      {currentStep === 0 && (
        <FileUpload
          onUploadComplete={handleFileUploadComplete}
          onContinue={handleContinue}
        />
      )}

      {currentStep === 1 && policyData && (
        <PolicyDataForm
          initialData={policyData}
          onDataUpdate={handleDataUpdate}
          onBack={handleBack}
          onContinue={handleContinue}
        />
      )}

      {currentStep === 2 && (
        <AuctionDurationSelect
          selectedDuration={auctionDuration}
          onDurationSelect={handleAuctionDurationSelect}
          onBack={handleBack}
          onContinue={handleContinue}
        />
      )}

      {currentStep === 3 && policyData && (
        <PolicyReview
          policyData={policyData}
          auctionDuration={auctionDuration}
          onBack={handleBack}
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
        />
      )}

      {currentStep === 4 && (
        <PolicySuccess
          policyNumber={policyData?.policyNumber}
        />
      )}
    </div>
  );
}
