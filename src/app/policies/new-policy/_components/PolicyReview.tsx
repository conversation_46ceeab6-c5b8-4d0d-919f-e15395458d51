"use client";

import { <PERSON><PERSON><PERSON><PERSON>, CheckCircle, Clock, Check, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { addHours, format } from "date-fns";
import { es } from "date-fns/locale";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { PolicyData } from "../_hooks/useNewPolicy";

interface PolicyReviewProps {
  policyData: PolicyData;
  auctionDuration: 24 | 48 | 72 | null;
  onBack: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export function PolicyReview({
  policyData,
  auctionDuration,
  onBack,
  onSubmit,
  isSubmitting,
}: PolicyReviewProps) {
  const now = new Date();
  const auctionEndTime = auctionDuration
    ? addHours(now, auctionDuration)
    : null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatAuctionDate = (date: Date) => {
    return format(date, "EEEE d 'de' MMMM 'a las' HH:mm", { locale: es });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2
          className="text-2xl font-semibold tracking-tight"
        >
          Confirmar Información
        </h2>
        <p className="text-muted-foreground">
          Por favor, revisa toda la información antes de confirmar. Esto
          guardará la póliza y comenzará la subasta.
        </p>
      </div>

      <div className="space-y-6">
        <Accordion
          type="multiple"
          className="w-full"
          defaultValue={[
            "policy",
            "insured",
            "vehicle",
            "coverages",
            "auction",
          ]}
        >
          <AccordionItem value="policy">
            <AccordionTrigger
              className="text-lg font-bold hover:no-underline py-4 px-6 bg-zeeguros-green text-black rounded-t-lg"
            >
              Detalles de la Póliza
            </AccordionTrigger>
            <AccordionContent className="pt-0">
              <Card className="border-t-0 rounded-t-none">
                <CardContent className="pt-6">
                  <dl
                    className="grid grid-cols-2 gap-x-4 gap-y-4"
                  >
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Número de Póliza
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.policyNumber}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Tipo de Póliza
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.policyType}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Fecha de Inicio
                      </dt>
                      <dd className="text-sm font-medium">
                        {formatDate(policyData.effectiveDate)}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Fecha de Vencimiento
                      </dt>
                      <dd className="text-sm font-medium">
                        {formatDate(policyData.expirationDate)}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Forma de Pago
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.paymentMethod}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Prima Anual
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.premium} €
                      </dd>
                    </div>
                  </dl>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="insured">
            <AccordionTrigger
              className="text-lg font-bold hover:no-underline py-4 px-6 bg-zeeguros-green text-black"
            >
              Datos del Asegurado
            </AccordionTrigger>
            <AccordionContent className="pt-0">
              <Card className="border-t-0 rounded-t-none">
                <CardContent className="pt-6">
                  <dl
                    className="grid grid-cols-2 gap-x-4 gap-y-4"
                  >
                    <div className="col-span-2">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Nombre
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredName}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        DNI/NIE/NIF
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredDNI}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Género
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredGender}
                      </dd>
                    </div>
                    <div className="col-span-2">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Dirección
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredAddress}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Código Postal
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredPostalCode}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        País
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredCountry}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Email
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredEmail}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Teléfono
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.insuredPhone}
                      </dd>
                    </div>
                  </dl>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="vehicle">
            <AccordionTrigger
              className="text-lg font-bold hover:no-underline py-4 px-6 bg-zeeguros-green text-black"
            >
              Datos del Vehículo
            </AccordionTrigger>
            <AccordionContent className="pt-0">
              <Card className="border-t-0 rounded-t-none">
                <CardContent className="pt-6">
                  <dl
                    className="grid grid-cols-2 gap-x-4 gap-y-4"
                  >
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Marca
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleMake}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Modelo
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleModel}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Año
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleYear}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Combustible
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleFuel}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Matrícula
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehiclePlate}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Bastidor (VIN)
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleVin}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Uso del Vehículo
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleUse}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Lugar de Aparcamiento
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleStorage}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt
                        className="text-sm text-muted-foreground"
                      >
                        Kilometraje Anual
                      </dt>
                      <dd className="text-sm font-medium">
                        {policyData.vehicleAnnualKm} km
                      </dd>
                    </div>
                  </dl>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="coverages">
            <AccordionTrigger
              className="text-lg font-bold hover:no-underline py-4 px-6 bg-zeeguros-green text-black"
            >
              Coberturas y Garantías
            </AccordionTrigger>
            <AccordionContent className="pt-0">
              <Card className="border-t-0 rounded-t-none">
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    {policyData.coverages.map((coverage) => (
                      <div
                        key={coverage.name}
                        className="flex items-center justify-between"
                      >
                        <div>
                          <p className="font-medium">
                            {coverage.name}
                          </p>
                          {coverage.limit && (
                            <p
                              className="text-sm text-muted-foreground"
                            >
                              Límite: {coverage.limit}
                            </p>
                          )}
                        </div>
                        <div
                          className="flex items-center gap-2"
                        >
                          {coverage.included ? (
                            <div
                              className="text-green-600 flex items-center gap-1"
                            >
                              <Check className="w-4 h-4" />
                              <span>INCLUIDO</span>
                            </div>
                          ) : (
                            <div
                              className="text-red-500 flex items-center gap-1"
                            >
                              <X className="w-4 h-4" />
                              <span>NO INCLUIDO</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          {auctionDuration && auctionEndTime && (
            <AccordionItem value="auction">
              <AccordionTrigger
                className="text-lg font-bold hover:no-underline py-4 px-6 bg-zeeguros-green text-black"
              >
                Información de la Subasta
              </AccordionTrigger>
              <AccordionContent className="pt-0">
                <Card className="border-t-0 rounded-t-none">
                  <CardContent className="pt-6">
                    <dl
                      className="grid grid-cols-1 gap-x-4 gap-y-4"
                    >
                      <div className="col-span-1">
                        <dt
                          className="text-sm text-muted-foreground"
                        >
                          Duración de la Subasta
                        </dt>
                        <dd
                          className="text-sm font-medium flex items-center"
                        >
                          <Clock className="h-3 w-3 mr-1" />
                          {auctionDuration} horas
                        </dd>
                      </div>
                      <div className="col-span-1">
                        <dt
                          className="text-sm text-muted-foreground"
                        >
                          Inicio de la Subasta
                        </dt>
                        <dd className="text-sm font-medium">
                          {formatAuctionDate(now)}
                        </dd>
                      </div>
                      <div className="col-span-1">
                        <dt
                          className="text-sm text-muted-foreground"
                        >
                          Fin de la Subasta
                        </dt>
                        <dd className="text-sm font-medium">
                          {formatAuctionDate(auctionEndTime)}
                        </dd>
                      </div>
                    </dl>
                  </CardContent>
                </Card>
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>

        <div className="flex justify-between">
          <Button
            type="button"
            className="btn-zeeguros-back"
            onClick={onBack}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Atrás
          </Button>
          <Button
            type="button"
            onClick={onSubmit}
            disabled={isSubmitting}
            className="btn-zeeguros"
          >
            {isSubmitting ? "Guardando..." : "Confirmar y crear subasta"}
            {!isSubmitting && (
              <CheckCircle className="ml-2 h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
