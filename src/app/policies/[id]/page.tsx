"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Car, Bike } from "lucide-react";
import Link from "next/link";
import { getPolicyDetails } from "@/lib/actions/getPolicyDetails";

interface PolicyDetails {
  id: string;
  type: string;
  icon: JSX.Element;
  policyNumber: string;
  effectiveDate: string;
  expirationDate: string;
  insuredName: string;
  carrier: string;
  status: string;
  premium: string;
  coverages: {
    name: string;
    limit: string;
    deductible: string;
  }[];
  documents: {
    name: string;
    date: string;
    url: string;
  }[];
}

export default function PolicyDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [policy, setPolicy] = useState<PolicyDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const policyId = params.id as string;

  useEffect(() => {
    const fetchPolicyDetails = async () => {
      try {
        setIsLoading(true);

        // Get policy from database using server action
        const data = await getPolicyDetails(policyId);

        if (!data || 'error' in data) {
          console.error("Error fetching policy:", data?.error || "No data returned");
          setIsLoading(false);
          return;
        }

        // Transform data to match our PolicyDetails interface
        const startDate = data.startDate ? new Date(data.startDate) : new Date();
        const endDate = data.endDate ? new Date(data.endDate) : new Date();
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = endDate.toISOString().split('T')[0];

        const policyDetails: PolicyDetails = {
          id: data.id,
          type: data.vehicle.type === "CAR" ? "Car Insurance" : "Motorcycle Insurance",
          icon: data.vehicle.type === "CAR" ? <Car className="h-5 w-5" /> : <Bike className="h-5 w-5" />,
          policyNumber: data.id.substring(0, 8).toUpperCase(),
          effectiveDate: startDateStr,
          expirationDate: endDateStr,
          insuredName: data.customer?.firstName
            ? `${data.customer.firstName} ${data.customer.lastName || ""}`
            : "Usuario",
          carrier: "Zeeguros",
          status: "Activa",
          premium: `${data.premium}€`,
          coverages: data.coverages?.map((coverage: any) => ({
            name: coverage.name,
            limit: coverage.insuredAmount ? `${coverage.insuredAmount}€` : "Incluido",
            deductible: "N/A"
          })) || [],
          documents: [
            {
              name: "Documento de Póliza",
              date: startDateStr,
              url: data.pdfUrl || "#"
            },
          ],
        };

        setPolicy(policyDetails);
      } catch (error) {
        console.error("Error fetching policy:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPolicyDetails();
  }, [policyId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!policy) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">
            Detalles de Póliza
          </h1>
        </div>

        <Card className="bg-white">
          <CardContent className="p-6 flex flex-col items-center justify-center py-10">
            <p className="text-muted-foreground text-center">No hay datos de póliza disponibles.</p>
            <Button variant="outline" className="mt-4" onClick={() => router.push('/policies')}>
              Volver a Mis Pólizas
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">
          Detalles de Póliza
        </h1>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl">Información de Póliza</CardTitle>
            <div className="rounded-full bg-primary/10 p-2">
              {policy.icon}
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Tipo de Póliza</p>
                <p className="font-medium">{policy.type}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Número de Póliza</p>
                <p className="font-medium">{policy.policyNumber}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Asegurado</p>
                <p className="font-medium">{policy.insuredName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Aseguradora</p>
                <p className="font-medium">{policy.carrier}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Fecha de Inicio</p>
                <p className="font-medium">{policy.effectiveDate}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Fecha de Vencimiento</p>
                <p className="font-medium">{policy.expirationDate}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Estado</p>
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-green-500"></span>
                  <p className="font-medium">{policy.status}</p>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Prima</p>
                <p className="font-medium">{policy.premium}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Documentos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {policy.documents.map((doc, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{doc.name}</p>
                    <p className="text-sm text-muted-foreground">{doc.date}</p>
                  </div>
                  <Button variant="outline" size="icon" asChild>
                    <Link href={doc.url}>
                      <Download className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Coberturas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Cobertura</th>
                  <th className="text-left py-3 px-4">Límite</th>
                  <th className="text-left py-3 px-4">Deducible</th>
                </tr>
              </thead>
              <tbody>
                {policy.coverages.map((coverage, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-3 px-4">{coverage.name}</td>
                    <td className="py-3 px-4">{coverage.limit}</td>
                    <td className="py-3 px-4">{coverage.deductible}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
