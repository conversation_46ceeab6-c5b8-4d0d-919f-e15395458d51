import { DashboardLayoutClient } from "@/app/(dashboard)/_components/dashboard-layout-client";
import { createClient } from "@/utils/supabase/server";
import { extractUserName } from "@/lib/user-metadata";

// Create a context to store user data
export const userDataContext: {
  user: any;
  userName: string;
  userInitials: string;
} = {
  user: null,
  userName: "",
  userInitials: ""
};

export default async function PoliciesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const { firstName, fullName } = user ? extractUserName(user) : { firstName: "", fullName: "Usuario" };

  // Get user initials for avatar
  const userInitials = firstName && firstName.charAt(0) || "Z";

  // Store user data in the context
  userDataContext.user = user;
  userDataContext.userName = fullName;
  userDataContext.userInitials = userInitials;

  return <DashboardLayoutClient userInitials={userInitials}>{children}</DashboardLayoutClient>;
}
