'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

// Handle user login
export async function login(formData: FormData) {
  const supabase = await createClient()

  const email = formData.get('email')
  const password = formData.get('password')

  // Input validation
  if (!email || !password) {
    return { error: 'El correo electrónico y la contraseña son requeridos' }
  }

  const data = {
    email: email as string,
    password: password as string,
  }

  const returnUrl = formData.get('returnUrl') as string || '/policies'

  try {
    const { error } = await supabase.auth.signInWithPassword(data)

    if (error) {
      // Map Supabase auth errors to user-friendly messages in Spanish
      switch (error.message) {
        case 'Invalid login credentials':
          return { error: 'Correo electrónico o contraseña incorrectos' }
        case 'Email not confirmed':
          return { error: 'Por favor, confirma tu correo electrónico antes de iniciar sesión' }
        default:
          return { error: error.message }
      }
    }
  } catch (err: any) {
    console.error('Error de conexión:', err)

    // Check for network connectivity errors
    if (err.cause?.code === 'ENOTFOUND' || err.cause?.code === 'ECONNREFUSED' || err.cause?.message?.includes('fetch failed')) {
      return {
        error: 'No se pudo conectar con el servidor. Por favor, verifica tu conexión a internet e inténtalo de nuevo. Si el problema persiste, contacta con soporte técnico.'
      }
    }

    // Handle other types of errors
    return {
      error: 'Ha ocurrido un error inesperado. Por favor, inténtalo de nuevo más tarde.'
    }
  }

  // Login successful - revalidate and redirect
  // Note: The redirect will throw a NEXT_REDIRECT error, which is expected behavior
  revalidatePath('/', 'layout')
  redirect(returnUrl)
}