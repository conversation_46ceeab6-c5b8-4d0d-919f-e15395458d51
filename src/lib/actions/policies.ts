'use server'

import { revalidatePath } from 'next/cache'
import { PolicyService } from '@/lib/services/policy.service'

// Create a new policy
export async function createPolicy(
  userId: string,
  name: string,
  policy_type: string,
  policy_number: string,
  description?: string,
  effective_date?: string,
  expiration_date?: string,
  carrier?: string,
  status?: string
) {
  try {
    console.log('Creating policy for user:', userId)

    const policy = await PolicyService.create({
      name,
      description: description || undefined,
      type: policy_type,
      policyNumber: policy_number,
      startDate: effective_date ? new Date(effective_date) : new Date(),
      endDate: expiration_date ? new Date(expiration_date) : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      carrier: carrier || 'Unspecified',
      status: status || 'ACTIVE',
      user: {
        connect: { id: userId }
      }
    })

    console.log('Policy created successfully:', policy)

    // Revalidate cache to update the policy list
    revalidatePath('/policies')

    return { success: true, data: policy }
  } catch (error) {
    console.error('Unexpected error creating policy:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido al crear la póliza'
    }
  }
}

// Update an existing policy
export async function updatePolicy(
  policyId: string,
  userId: string,
  updates: {
    name?: string
    description?: string
    policy_type?: string
    policy_number?: string
    effective_date?: string
    expiration_date?: string
    carrier?: string
    status?: string
  }
) {
  try {
    console.log('Updating policy:', policyId)

    // Verify that the policy exists and belongs to the user
    const existingPolicy = await PolicyService.getById(policyId)

    if (!existingPolicy) {
      return { success: false, error: 'Póliza no encontrada' }
    }

    if (existingPolicy.userId !== userId) {
      console.error('Unauthorized policy update attempt')
      return { success: false, error: 'No autorizado para actualizar esta póliza' }
    }

    // Update the policy using the service
    const updatedPolicy = await PolicyService.update(policyId, {
      name: updates.name,
      description: updates.description,
      type: updates.policy_type,
      policyNumber: updates.policy_number,
      startDate: updates.effective_date ? new Date(updates.effective_date) : undefined,
      endDate: updates.expiration_date ? new Date(updates.expiration_date) : undefined,
      carrier: updates.carrier,
      status: updates.status
    })

    console.log('Policy updated successfully:', updatedPolicy)

    // Revalidate cache to update the policy list
    revalidatePath('/policies')
    revalidatePath(`/policies/${policyId}`)

    return { success: true, data: updatedPolicy }
  } catch (error) {
    console.error('Unexpected error updating policy:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido al actualizar la póliza'
    }
  }
}

// Delete a policy
export async function deletePolicy(policyId: string, userId: string) {
  try {
    console.log('Deleting policy:', policyId)

    // Verify that the policy exists and belongs to the user
    const existingPolicy = await PolicyService.getById(policyId)

    if (!existingPolicy) {
      return { success: false, error: 'Póliza no encontrada' }
    }

    if (existingPolicy.userId !== userId) {
      console.error('Unauthorized policy deletion attempt')
      return { success: false, error: 'No autorizado para eliminar esta póliza' }
    }

    // Delete the policy using the service
    await PolicyService.delete(policyId)

    console.log('Policy deleted successfully')

    // Revalidate cache to update the policy list
    revalidatePath('/policies')

    return { success: true }
  } catch (error) {
    console.error('Unexpected error deleting policy:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido al eliminar la póliza'
    }
  }
}
