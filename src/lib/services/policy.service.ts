import { db } from "@/lib/db";
import type { Policy, Prisma } from "@prisma/client";

export const PolicyService = {
  // Create a new policy
  async create(data: Prisma.PolicyCreateInput): Promise<Policy> {
    return db.policy.create({
      data,
      include: {
        customer: true,
        broker: true,
        vehicle: true,
        coverages: true
      }
    });
  },

  // Get a policy by ID
  async getById(id: string): Promise<Policy | null> {
    return db.policy.findUnique({
      where: { id },
      include: {
        customer: {
          include: {
            user: true
          }
        },
        broker: {
          include: {
            user: true
          }
        },
        vehicle: true,
        coverages: true
      }
    });
  },

  // Get policies by customer ID
  async getByCustomerId(customerId: string): Promise<Policy[]> {
    return db.policy.findMany({
      where: { customerId },
      include: {
        broker: {
          include: {
            user: true
          }
        },
        vehicle: true,
        coverages: true
      }
    });
  },

  // Get policies by broker ID
  async getByBrokerId(brokerId: string): Promise<Policy[]> {
    return db.policy.findMany({
      where: { brokerId },
      include: {
        customer: {
          include: {
            user: true
          }
        },
        vehicle: true,
        coverages: true
      }
    });
  },

  // Get policies by vehicle ID
  async getByVehicleId(vehicleId: string): Promise<Policy[]> {
    return db.policy.findMany({
      where: { vehicleId },
      include: {
        customer: true,
        broker: true,
        coverages: true
      }
    });
  },

  // Get active policies (not expired)
  async getActive(): Promise<Policy[]> {
    return db.policy.findMany({
      where: {
        endDate: {
          gt: new Date()
        }
      },
      include: {
        customer: true,
        broker: true,
        vehicle: true,
        coverages: true
      }
    });
  },

  // Update a policy
  async update(id: string, data: Prisma.PolicyUpdateInput): Promise<Policy> {
    return db.policy.update({
      where: { id },
      data,
      include: {
        customer: true,
        broker: true,
        vehicle: true,
        coverages: true
      }
    });
  },

  // Delete a policy
  async delete(id: string): Promise<Policy> {
    return db.policy.delete({
      where: { id }
    });
  },

  // List policies with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.PolicyWhereInput;
    orderBy?: Prisma.PolicyOrderByWithRelationInput;
  }): Promise<{ policies: Policy[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [policies, total] = await Promise.all([
      db.policy.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          customer: {
            include: {
              user: true
            }
          },
          broker: {
            include: {
              user: true
            }
          },
          vehicle: true,
          coverages: true
        }
      }),
      db.policy.count({ where })
    ]);

    return { policies, total };
  }
};