# Zeeguros - Insurance Platform

![<PERSON><PERSON><PERSON><PERSON> Logo](public/logo-zeeguros-b.svg)

## Description

Zeeguros is a modern platform for car insurance policy management, designed to offer a seamless experience for both insured individuals and insurance brokers. The platform allows users to manage their policies, view coverage details, and participate in insurance auctions.

## Key Features

- **Secure Authentication**: Implemented with Supabase Auth
- **Profile Management**: User profile management with personal information
- **Dashboard**: Intuitive interface to visualize policies and notifications
- **Relational Database**: PostgreSQL managed by Prisma
- **Responsive Design**: Adaptable interface using Tailwind CSS and Shadcn UI

## Technology Stack

### Framework and Base
- **Next.js 15+**: React framework with hybrid rendering (SSR/CSR)
- **React 18+**: Library for building user interfaces
- **TypeScript**: Typed superset of JavaScript

### Database and ORM
- **Supabase**: Backend as a service (BaaS) platform
- **Prisma**: Modern ORM for TypeScript and Node.js
- **PostgreSQL**: Database management system

### UI and Styling
- **Tailwind CSS**: Utility-first CSS framework
- **Shadcn UI**: UI components based on Radix UI
- **Lucide Icons**: Modern icon library

## Architecture

```
/
├── public/               # Static files
├── src/
│   ├── app/             # Routes and pages (Next.js App Router)
│   │   ├── (auth)/      # Authentication routes
│   │   ├── (dashboard)/ # Dashboard routes
│   │   ├── api/         # Route Handlers (API)
│   │   └── _components/ # Shared components
│   ├── components/      # Reusable components
│   │   └── ui/          # UI components (shadcn)
│   ├── hooks/           # Custom hooks
│   ├── lib/             # Utilities and configurations
│   ├── styles/          # Global styles
│   └── supabase/        # Supabase configuration
├── prisma/              # Schema and migrations
└── changelogs/          # Change logs
```

## User Roles

- **Insured**: End user who manages policies
- **Broker**: Professional who manages multiple policies
- **Administrator**: Full access to the platform

## Installation and Development

### Prerequisites

- Node.js 18.17.0 or higher
- npm or yarn
- PostgreSQL database (or Supabase account)

### Setup

1. Clone the repository
   ```bash
   git clone https://github.com/your-username/zee-app-t3.git
   cd zee-app-t3
   ```

2. Install dependencies
   ```bash
   npm install
   ```

3. Configure environment variables
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with the necessary credentials:
   - `DATABASE_URL`: PostgreSQL connection URL
   - `DIRECT_URL`: Direct URL for Prisma
   - `NEXT_PUBLIC_SUPABASE_URL`: Supabase project URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Supabase anonymous key
   - `NEXTAUTH_URL`: Base URL (in production)
   - `NEXTAUTH_SECRET`: Secret for session cookies

4. Configure the database
   ```bash
   npx prisma migrate dev
   ```

5. Start the development server
   ```bash
   npm run dev
   ```

### Available Scripts

- `npm run dev`: Start the development server
- `npm run build`: Build the application
- `npm run start`: Start in production mode
- `npm run lint`: Run the linter
- `npm run format`: Format the code
- `npm run db:push`: Synchronize the Prisma schema
- `npm run db:studio`: Open Prisma Studio

## Production Considerations

- Configure environment variables on the deployment platform
- Set up custom domain and SSL
- Configure CORS policies in Supabase
- Implement monitoring system

## Contribution

1. Create a feature branch (`git checkout -b feature/amazing-feature`)
2. Commit your changes (`git commit -m 'Add some amazing feature'`)
3. Push to the branch (`git push origin feature/amazing-feature`)
4. Open a Pull Request

## Change History

See [changelogs](./changelogs/) for a detailed record of changes.

## License

This project is owned by Zeeguros. All rights reserved.
