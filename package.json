{"name": "<PERSON><PERSON><PERSON><PERSON>-next", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbopack", "lint": "next lint", "start": "next start", "db:push": "prisma db push", "db:studio": "prisma studio", "postinstall": "prisma generate", "migrate:dev": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "migrate:reset": "prisma migrate reset", "migrate:status": "prisma migrate status", "migrate": "prisma migrate dev", "db:seed": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.6.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.7", "@t3-oss/env-nextjs": "^0.9.2", "@tanstack/react-query": "^4.36.1", "@trpc/client": "^10.45.1", "@trpc/next": "^10.45.1", "@trpc/react-query": "^10.45.1", "@trpc/server": "^10.45.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "lucide-react": "^0.488.0", "next": "^15.3.0", "next-themes": "^0.4.6", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.55.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "superjson": "^2.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/eslint": "^8.56.2", "@types/node": "^20.11.20", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^15.3.0", "postcss": "^8.4.32", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "prisma": "^6.6.0", "tailwindcss": "^3.4.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "ct3aMetadata": {"initVersion": "7.26.0"}}