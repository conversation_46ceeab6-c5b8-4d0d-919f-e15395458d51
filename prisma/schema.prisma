// === GENERATORS ===
generator client {
  provider        = "prisma-client-js"
  output          = "../node_modules/.prisma/client"
  previewFeatures = ["multiSchema"]
}

generator prisma_client_seed {
  provider        = "prisma-client-js"
  output          = "../node_modules/.prisma/client"
  previewFeatures = ["multiSchema"]
  seed            = "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"
}

// === DATASOURCE ===
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public"]
}

// === ENUMS ===

enum Role {
  CUSTOMER
  BROKER

  @@schema("public")
}

enum KYCStatus {
  PENDING
  VERIFIED
  REJECTED

  @@schema("public")
}

enum PaymentPeriod {
  MONTHLY
  QUARTERLY
  SEMIANNUAL
  ANNUAL

  @@schema("public")
}

enum VehicleType {
  CAR
  MOTORCYCLE

  @@schema("public")
}

enum FuelType {
  GASOLINE
  DIESEL
  ELECTRIC
  HYBRID

  @@schema("public")
}

enum GarageType {
  STREET
  SHARED_UNGUARDED
  SHARED_GUARDED
  PRIVATE

  @@schema("public")
}

enum UsageType {
  PRIVATE_OCCASIONAL
  PRIVATE_REGULAR
  PROFESSIONAL_OCCASIONAL
  PROFESSIONAL_REGULAR

  @@schema("public")
}

enum OwnerRelation {
  MAIN_DRIVER
  SPOUSE
  CHILD
  SIBLING
  PARENT
  OTHER

  @@schema("public")
}

enum KmRange {
  UP_TO_2000
  FROM_2000_TO_4000
  FROM_4000_TO_6000
  FROM_6000_TO_8000
  FROM_8000_TO_10000
  FROM_10000_TO_12000
  FROM_12000_TO_14000
  FROM_14000_TO_16000
  FROM_16000_TO_18000
  FROM_18000_TO_20000
  FROM_20000_TO_22000
  FROM_22000_TO_24000
  FROM_24000_TO_26000
  FROM_26000_TO_28000
  FROM_28000_TO_30000
  FROM_30000_TO_32000
  FROM_32000_TO_34000
  FROM_34000_TO_36000
  FROM_36000_TO_38000
  FROM_38000_TO_40000
  FROM_40000_TO_45000
  FROM_45000_TO_50000
  OVER_50000

  @@schema("public")
}

enum GuaranteeType {
  MANDATORY_LIABILITY
  VOLUNTARY_LIABILITY
  LEGAL_DEFENSE
  DRIVER_ACCIDENTS
  TRAVEL_ASSISTANCE
  GLASS_BREAKAGE
  FIRE
  THEFT
  TOTAL_LOSS_THEFT
  TOTAL_LOSS_DAMAGE
  TOTAL_LOSS_FIRE
  VEHICLE_DAMAGE
  EXTRAORDINARY_RISKS_PERSONS
  EXTRAORDINARY_RISKS_VEHICLE
  FINES_MANAGEMENT
  LICENSE_SUSPENSION
  COLLISION_WITH_ANIMALS
  LEGAL_REPRESENTATION_EXTENSION
  PERSONAL_BELONGINGS
  IMMOBILIZATION
  LICENSE_SUSPENSION_SUBSIDY
  WEATHER_DAMAGE
  TOWING_FROM_KM0
  ADVANCE_COMPENSATION
  LOST_KEYS
  REPATRIATION
  VEHICLE_REPLACEMENT
  PSYCHOLOGICAL_ASSISTANCE
  ASSISTIVE_EQUIPMENT_RENTAL
  HANDYMAN_SERVICE
  LOAD_LIABILITY

  @@schema("public")
}

enum PartyRole {
  POLICYHOLDER
  OWNER
  MAIN_DRIVER
  ADDITIONAL_DRIVER

  @@schema("public")
}

// === MODELS ===

model User {
  id            String           @id @default(uuid())
  email         String           @unique
  phone         String?          @unique
  role          Role
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  auctions      Auction[]        @relation("UserAuctions")
  brokerProfile BrokerProfile?
  customer      CustomerProfile?
  policies      Policy[]

  @@index([email])
  @@schema("public")
}

model CustomerProfile {
  id                String             @id @default(uuid())
  userId            String             @unique
  firstName         String
  lastName          String
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  policies          Policy[]
  insuranceHistory  InsuranceHistory[]

  @@index([userId])
  @@schema("public")
}

model InsuranceHistory {
  id               String   @id @default(uuid())
  customerId       String
  previousCompany  String
  yearsInsured     Int
  claimsLast5Years Int
  customer         CustomerProfile @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([customerId])
  @@schema("public")
}

model BrokerProfile {
  id                   String                @id @default(uuid())
  userId               String                @unique
  registrationClass    String
  controlAuthority     String
  registrationKey      String                @unique
  registrationDate     DateTime
  legalName            String
  identifier           String                @unique
  operationScope       String
  isAuthorizedByOther  Boolean
  isComplementary      Boolean
  isGroupAgent         Boolean
  kycStatus            KYCStatus             @default(PENDING)
  bids                 Bid[]
  billingAddress       BillingAddress?
  user                 User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  identityVerification IdentityVerification?
  policies             Policy[]

  @@index([userId])
  @@index([registrationKey])
  @@index([identifier])
  @@schema("public")
}

model BillingAddress {
  id           String        @id @default(uuid())
  brokerId     String        @unique
  address      String
  postalCode   String
  province     String
  municipality String
  country      String
  website      String?
  broker       BrokerProfile @relation(fields: [brokerId], references: [id], onDelete: Cascade)

  @@index([brokerId])
  @@schema("public")
}

model IdentityVerification {
  id           String        @id @default(uuid())
  brokerId     String        @unique
  frontIdUrl   String
  backIdUrl    String
  faceMatchUrl String?
  verifiedAt   DateTime?
  broker       BrokerProfile @relation(fields: [brokerId], references: [id], onDelete: Cascade)

  @@index([brokerId])
  @@schema("public")
}

model Policy {
  id                    String           @id @default(uuid())
  customerId            String
  brokerId              String
  brokerName            String?
  brokerDgsCode         String?
  vehicleId             String
  pdfUrl                String
  startDate             DateTime
  endDate               DateTime
  paymentPeriod         PaymentPeriod
  premium               Float
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt
  userId                String
  policyType            String
  insurerName           String
  policyNumber          String
  productName           String?
  deductibleAmount      Float?
  deductibleNotes       String?
  paymentMethod         String?
  iban                  String?
  bic                   String?
  coverages             Coverage[]
  broker                BrokerProfile    @relation(fields: [brokerId], references: [id])
  customer              CustomerProfile  @relation(fields: [customerId], references: [id])
  user                  User             @relation(fields: [userId], references: [id])
  vehicle               Vehicle          @relation(fields: [vehicleId], references: [id])
  insuredParties        InsuredParty[]
  telematicsDevice      TelematicsDevice?
  policyClauses         PolicyClause[]
  policyConsents        PolicyConsent[]

  @@index([customerId])
  @@index([brokerId])
  @@index([vehicleId])
  @@schema("public")
}

model InsuredParty {
  id         String     @id @default(uuid())
  policyId   String
  fullName   String
  dni        String
  role       PartyRole
  address    String?
  postalCode String?
  regionName String?
  country    String?
  policy     Policy     @relation(fields: [policyId], references: [id], onDelete: Cascade)

  @@index([policyId])
  @@schema("public")
}

model TelematicsDevice {
  id           String   @id @default(uuid())
  policyId     String   @unique
  installed    Boolean
  installedAt  DateTime?
  disconnected Boolean  @default(false)
  deviceType   String
  policy       Policy   @relation(fields: [policyId], references: [id], onDelete: Cascade)

  @@schema("public")
}

model PolicyClause {
  id        String   @id @default(uuid())
  policyId  String
  title     String
  content   String
  policy    Policy   @relation(fields: [policyId], references: [id], onDelete: Cascade)

  @@index([policyId])
  @@schema("public")
}

model PolicyConsent {
  id        String   @id @default(uuid())
  policyId  String
  method    String
  timestamp DateTime
  policy    Policy   @relation(fields: [policyId], references: [id], onDelete: Cascade)

  @@index([policyId])
  @@schema("public")
}

model Vehicle {
  id                          String        @id @default(uuid())
  type                        VehicleType 
  licensePlate                String        @unique
  brand                       String
  model                       String
  version                     String?
  year                        Int
  firstRegistrationDate       DateTime
  fuelType                    FuelType 
  powerKw                     Float?
  powerCv                     Float?
  garageType                  GarageType 
  usageType                   UsageType
  usageDescription            String?
  customKmPerYear             Int? 
  initialKm                   Int? 
  chassisNumber               String        @unique
  kmPerYear                   KmRange
  isLeased                    Boolean
  ownerRelation               OwnerRelation
  seats                       Int
  declaredValue               Float? 
  fixedAccessoriesCoveredUpTo Float?
  circulationRegionCode       String? 
  circulationRegionName       String?
  circulationCountry          String?
  maxAuthorizedWeightKg       Float?
  auctions                    Auction[]
  policies                    Policy[]

  @@index([licensePlate])
  @@index([chassisNumber])
  @@schema("public")
}

model Coverage {
  id                      String        @id @default(uuid())
  policyId                String
  guaranteeName           GuaranteeType
  included                Boolean
  insuredAmount           Float?
  description             String?
  limitFormula            String?
  beneficiaryDescription  String?
  policy                  Policy        @relation(fields: [policyId], references: [id], onDelete: Cascade)

  @@index([policyId])
  @@schema("public")
}

model Auction {
  id        String   @id @default(uuid())
  userId    String
  vehicleId String
  startDate DateTime
  endDate   DateTime
  isClosed  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation("UserAuctions", fields: [userId], references: [id], onDelete: Cascade)
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id])
  bids      Bid[]

  @@index([userId])
  @@index([vehicleId])
  @@schema("public")
}

model Bid {
  id        String        @id @default(uuid())
  auctionId String
  brokerId  String
  amount    Float
  createdAt DateTime      @default(now())
  auction   Auction       @relation(fields: [auctionId], references: [id], onDelete: Cascade)
  broker    BrokerProfile @relation(fields: [brokerId], references: [id])

  @@index([auctionId])
  @@index([brokerId])
  @@schema("public")
}
